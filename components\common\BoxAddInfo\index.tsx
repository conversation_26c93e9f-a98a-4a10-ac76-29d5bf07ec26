import {
    <PERSON><PERSON>,
    <PERSON>,
    CardBody,
    <PERSON><PERSON><PERSON><PERSON>,
    Col,
    Label,
    Row,
} from 'reactstrap';
import { useState } from 'react';
import DropdownActionMenu from '../DropdownActionMenu';

interface BoxAddInfoItem {
    title?: string;
    infoAdd?: string;
    data: {
        label: string;
        value: string;
        boxColor?: boolean;
    }[];
    onViewQuick?: () => void;
    onViewDetail?: () => void;
    onDelete?: () => void;
}

interface BoxAddInfoProps {
    title: string;
    length: number;
    onAdd?: () => void;
    content: BoxAddInfoItem[];
}

const BoxAddInfo = ({
    title,
    length,
    content = [],
    onAdd,
}: BoxAddInfoProps) => {
    const [showAll, setShowAll] = useState(false);

    const displayedContent = showAll ? content : content.slice(0, 1);
    const hasMoreItems = content.length > 1;

    return (
        <Col md={12}>
            <Card className='mb-3'>
                <CardHeader>
                    <Row>
                        <Col md={9}>
                            <h5 className='mb-0'>
                                {title} ({length})
                            </h5>
                        </Col>
                        <Col md={3}>
                            <Button
                                color='success'
                                size='sm'
                                onClick={onAdd}
                                style={{
                                    backgroundColor: '#ffffff',
                                    borderColor: '#0ab39c',
                                    color: '#0ab39c',
                                }}
                            >
                                <i className='ri-add-line align-middle'></i>
                                Thêm
                            </Button>
                        </Col>
                    </Row>
                </CardHeader>
                <CardBody>
                    {displayedContent.map((item, index) => (
                        <div
                            key={index}
                            className='mb-3'
                            style={{
                                borderBottom: '1px solid #e9ebec',
                            }}
                        >
                            <div
                                className='rounded'
                                style={{
                                    paddingLeft: '10px',
                                    marginRight: '10px',
                                }}
                            >
                                <div className='d-flex align-items-center justify-content-between mb-3'>
                                    <Col md={9}>
                                        <div
                                            style={{
                                                fontSize: '15px',
                                                fontWeight: '500',
                                            }}
                                        >
                                            {item.title}
                                        </div>
                                    </Col>
                                    <Col
                                        md={3}
                                        style={{
                                            display: 'flex',
                                            justifyContent: 'flex-end',
                                        }}
                                    >
                                        <DropdownActionMenu
                                            actions={[
                                                {
                                                    icon: 'ri-eye-line',
                                                    label: 'Xem nhanh',
                                                    onClick:
                                                        item.onViewQuick ||
                                                        (() => {}),
                                                },
                                                {
                                                    icon: 'ri-eye-fill',
                                                    label: 'Xem chi tiết',
                                                    onClick:
                                                        item.onViewDetail ||
                                                        (() => {}),
                                                },
                                                {
                                                    icon: 'ri-delete-bin-line',
                                                    label: 'Xóa',
                                                    onClick:
                                                        item.onDelete ||
                                                        (() => {}),
                                                    className: 'text-danger',
                                                },
                                            ]}
                                            toggleIcon='ri-more-2-fill'
                                        />
                                    </Col>
                                </div>
                                {item.infoAdd && (
                                    <small className='text-muted d-block mb-2 mt-2'>
                                        {item.infoAdd}
                                    </small>
                                )}
                                {item.data.map((dataItem, dataIndex) => (
                                    <div
                                        key={dataIndex}
                                        className='d-flex align-items-center gap-2 mb-3 mt-3'
                                    >
                                        <Label
                                            className='mb-0'
                                            style={{ minWidth: 'fit-content' }}
                                        >
                                            {dataItem.label}:
                                        </Label>
                                        {dataItem.boxColor ? (
                                            <div
                                                style={{
                                                    display: 'inline-flex',
                                                    alignItems: 'center',
                                                    backgroundColor: '#daf4f0',
                                                    borderColor: '#daf4f0',
                                                    borderRadius: '5px',
                                                    color: '#0ab39c',
                                                    padding: '4px 12px',
                                                    fontSize: '12px',
                                                    fontWeight: '500',
                                                }}
                                            >
                                                {dataItem.value}
                                            </div>
                                        ) : (
                                            <span className='mb-0'>
                                                {dataItem.value}
                                            </span>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}

                    {hasMoreItems && (
                        <div className='mt-3'>
                            <Button
                                color='primary'
                                size='sm'
                                onClick={() => setShowAll(!showAll)}
                                style={{
                                    width: '100%',
                                    backgroundColor: '#ffffff',
                                    borderColor: '#0ab39c',
                                    color: '#0ab39c',
                                    padding: '8px 12px',
                                    fontSize: '14px',
                                }}
                            >
                                {showAll ? <>Thu gọn</> : <>Xem thêm</>}
                            </Button>
                        </div>
                    )}

                    {content.length === 0 && (
                        <div className='text-center text-muted py-4'>
                            Chưa có dữ liệu
                        </div>
                    )}
                </CardBody>
            </Card>
        </Col>
    );
};

export default BoxAddInfo;
