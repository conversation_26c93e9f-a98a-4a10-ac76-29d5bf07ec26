import { useState } from 'react';
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    Card<PERSON>ooter,
    CardHeader,
    Col,
    Label,
} from 'reactstrap';
import DropdownActionMenu from '@/components/common/DropdownActionMenu';

import { ICustomer } from '@/apis/customer/customer.type';
import BoxAddInfo from '@/components/common/BoxAddInfo';

interface FileInfoProps {
    data: ICustomer;
}
const FileInfo = ({ data }: FileInfoProps) => {
    const [showAllPartners, setShowAllPartners] = useState(false);
    const [showAllPurchaseContacts, setShowAllPurchaseContacts] =
        useState(false);
    const [showAllUsageContacts, setShowAllUsageContacts] = useState(false);
    const handleCreate = () => {};
    return (
        <Col lg={3}>
            <Card className='mb-3'>
                <CardHeader className='border-bottom-0'>
                    <div className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>File đính kèm (2)</h5>
                        <Button
                            color='success'
                            size='sm'
                            className='btn-sm'
                            style={{
                                backgroundColor: '#0ab39c',
                                border: 'none',
                            }}
                        >
                            <i className='ri-add-line align-middle'></i> Thêm
                        </Button>
                    </div>
                </CardHeader>
                <CardBody>
                    {[
                        {
                            name: 'CRM.docx',
                            icon: 'ri-file-text-line',
                            date: '11/01/2025',
                        },
                        {
                            name: 'CRM.ppt',
                            icon: 'ri-file-ppt-line',
                            date: '11/01/2025',
                        },
                    ].map((file, index) => (
                        <div className='mb-3' key={index}>
                            <div className='d-flex align-items-center mb-2'>
                                <i
                                    className={`${file.icon} text-primary fs-18 me-2`}
                                ></i>
                                <div className='flex-grow-1'>
                                    <h6 className='mb-0'>{file.name}</h6>
                                    <small className='text-muted'>
                                        Ngày tải lên: {file.date}
                                    </small>
                                </div>
                                <div className='d-flex'>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon me-1'
                                    >
                                        <i className='ri-download-line'></i>
                                    </Button>
                                    <Button
                                        color='light'
                                        size='sm'
                                        className='btn-icon'
                                    >
                                        <i className='ri-delete-bin-line'></i>
                                    </Button>
                                </div>
                            </div>
                        </div>
                    ))}

                    <Button color='info' outline block>
                        Xem thêm
                    </Button>
                </CardBody>
            </Card>
            <BoxAddInfo
                title='Đối tác thương mại'
                length={data.detailTradePartnerCompanies.length || 0}
                content={data.detailTradePartnerCompanies.map((item) => ({
                    title: item.name,
                    data: [
                        {
                            label: 'Email',
                            value: item.email,
                            boxColor: false,
                        },
                        {
                            label: 'Số điện thoại',
                            value: item.phoneNumber,
                            boxColor: false,
                        },
                    ],
                    // onViewQuick: item.onViewQuick,
                    // onViewDetail: item.onViewDetail,
                    // onDelete: item.onDelete,
                }))}
                // onAdd={handleAddNewItem}
            />
            {data.detailTradePartnerCompanies.length > 0 && (
                <Card className='mb-3'>
                    <CardHeader className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>
                            Đối tác thương mại
                            {data.detailTradePartnerCompanies.length > 1 && (
                                <>({data.detailTradePartnerCompanies.length})</>
                            )}
                        </h5>
                        <DropdownActionMenu
                            actions={[
                                {
                                    icon: 'ri-add-line',
                                    label: 'Tạo mới',
                                    onClick: () => handleCreate(),
                                },
                            ]}
                            toggleIcon='ri-more-2-fill'
                        />
                    </CardHeader>
                    <CardBody>
                        {(showAllPartners
                            ? data.detailTradePartnerCompanies
                            : data.detailTradePartnerCompanies.slice(0, 1)
                        ).map((item, idx, arr) => (
                            <div key={item?.id || idx}>
                                <h6 className='mb-3'>{item?.name}</h6>
                                <div className='mb-2'>
                                    <small className='text-muted d-block'>
                                        Email:
                                    </small>
                                    <div>{item?.email}</div>
                                </div>
                                <div className='mb-2'>
                                    <small className='text-muted d-block'>
                                        Số điện thoại:
                                    </small>
                                    <div>{item?.phoneNumber}</div>
                                </div>
                                {idx < arr.length - 1 && (
                                    <hr className='my-3' />
                                )}
                            </div>
                        ))}
                    </CardBody>
                    {data.detailTradePartnerCompanies.length > 1 && (
                        <CardFooter>
                            <Label
                                style={{ cursor: 'pointer' }}
                                onClick={() =>
                                    setShowAllPartners(!showAllPartners)
                                }
                            >
                                {showAllPartners ? 'Thu lại' : 'Xem thêm'}
                            </Label>
                        </CardFooter>
                    )}
                </Card>
            )}
            {data.purchaseContactDetailCompanies.length > 0 && (
                <Card>
                    <CardHeader className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>
                            Phòng mua hàng
                            {data.purchaseContactDetailCompanies.length > 0 && (
                                <>
                                    (
                                    {data.purchaseContactDetailCompanies.length}
                                    )
                                </>
                            )}
                        </h5>
                        <DropdownActionMenu
                            actions={[
                                {
                                    icon: 'ri-add-line',
                                    label: 'Tạo mới',
                                    onClick: () => handleCreate(),
                                },
                            ]}
                            toggleIcon='ri-more-2-fill'
                        />
                    </CardHeader>
                    <CardBody>
                        {(showAllPurchaseContacts
                            ? data.purchaseContactDetailCompanies
                            : data.purchaseContactDetailCompanies.slice(0, 1)
                        ).map((contact, index, arr) => (
                            <div key={contact.id || index} className='mb-4'>
                                <h6>{contact.name || 'Không có tên'}</h6>
                                <div className='mb-2'>
                                    <small className='text-muted d-block'>
                                        Email:
                                    </small>
                                    <div>
                                        {contact.email || 'Chưa có email'}
                                    </div>
                                </div>
                                <div className='mb-2'>
                                    <small className='text-muted d-block'>
                                        Số điện thoại:
                                    </small>
                                    <div>
                                        {contact.phoneNumber || 'Chưa có số'}
                                    </div>
                                </div>
                                {index < arr.length - 1 && (
                                    <hr className='my-3' />
                                )}
                            </div>
                        ))}
                    </CardBody>
                    {data.purchaseContactDetailCompanies.length > 1 && (
                        <CardFooter style={{ height: '40px' }}>
                            <Label
                                style={{ cursor: 'pointer' }}
                                onClick={() =>
                                    setShowAllPurchaseContacts((prev) => !prev)
                                }
                            >
                                {showAllPurchaseContacts
                                    ? 'Thu lại'
                                    : 'Xem thêm'}
                            </Label>
                        </CardFooter>
                    )}
                </Card>
            )}
            {data.usageContactDetailCompanies.length > 0 && (
                <Card>
                    <CardHeader className='d-flex align-items-center'>
                        <h5 className='mb-0 flex-grow-1'>
                            Phòng sử dụng
                            {data.usageContactDetailCompanies.length > 0 && (
                                <>({data.usageContactDetailCompanies.length})</>
                            )}
                        </h5>
                        <DropdownActionMenu
                            actions={[
                                {
                                    icon: 'ri-add-line',
                                    label: 'Tạo mới',
                                    onClick: () => handleCreate(),
                                },
                            ]}
                            toggleIcon='ri-more-2-fill'
                        />
                    </CardHeader>
                    <CardBody>
                        {(showAllUsageContacts
                            ? data.usageContactDetailCompanies
                            : data.usageContactDetailCompanies.slice(0, 1)
                        ).map((contact, index, arr) => (
                            <div key={contact.id || index} className='mb-4'>
                                <h6>{contact.name || 'Không có tên'}</h6>
                                <div className='mb-2'>
                                    <small className='text-muted d-block'>
                                        Email:
                                    </small>
                                    <div>
                                        {contact.email || 'Chưa có email'}
                                    </div>
                                </div>
                                <div className='mb-2'>
                                    <small className='text-muted d-block'>
                                        Số điện thoại:
                                    </small>
                                    <div>
                                        {contact.phoneNumber || 'Chưa có số'}
                                    </div>
                                </div>
                                {index < arr.length - 1 && (
                                    <hr className='my-3' />
                                )}
                            </div>
                        ))}
                    </CardBody>
                    {data.usageContactDetailCompanies.length > 1 && (
                        <CardFooter style={{ height: '40px' }}>
                            <Label
                                style={{ cursor: 'pointer' }}
                                onClick={() =>
                                    setShowAllUsageContacts((prev) => !prev)
                                }
                            >
                                {showAllUsageContacts ? 'Thu lại' : 'Xem thêm'}
                            </Label>
                        </CardFooter>
                    )}
                </Card>
            )}
        </Col>
    );
};

export default FileInfo;
